"""
Main RPA script template with Playwright and stealth capabilities.
"""
import asyncio
import sys
from pathlib import Path
from loguru import logger

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from src.utils.browser_manager import BrowserManager

def setup_logging():
    """Configure logging with loguru"""
    # Remove default handler
    logger.remove()
    
    # Add console handler
    logger.add(
        sys.stderr,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # Add file handler
    logger.add(
        settings.log_file,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days"
    )

async def example_automation():
    """Example automation task - replace with your actual automation logic"""
    async with BrowserManager() as browser_manager:
        try:
            # Create a new browser context
            context = await browser_manager.create_context()
            
            # Create a new page
            page = await browser_manager.create_page(context)
            
            logger.info("Starting example automation...")
            
            # Navigate to a website
            await page.goto("https://httpbin.org/user-agent")
            await browser_manager.random_delay()
            
            # Take a screenshot
            await browser_manager.take_screenshot("example_page.png")
            
            # Get page content
            content = await page.content()
            logger.info(f"Page loaded successfully. Content length: {len(content)}")
            
            # Example: Extract user agent information
            user_agent_element = await page.query_selector("pre")
            if user_agent_element:
                user_agent_text = await user_agent_element.text_content()
                logger.info(f"Detected User Agent: {user_agent_text}")
            
            # Example: Navigate to another page
            await page.goto("https://httpbin.org/headers")
            await browser_manager.random_delay()
            
            # Take another screenshot
            await browser_manager.take_screenshot("headers_page.png")
            
            # Example: Extract headers information
            headers_element = await page.query_selector("pre")
            if headers_element:
                headers_text = await headers_element.text_content()
                logger.info("Headers information extracted successfully")
                
                # Save to file
                output_file = f"{settings.output_dir}/headers_info.txt"
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(headers_text)
                logger.info(f"Headers saved to: {output_file}")
            
            logger.info("Example automation completed successfully!")
            
        except Exception as e:
            logger.error(f"Automation failed: {e}")
            # Take screenshot on error for debugging
            try:
                await browser_manager.take_screenshot("error_screenshot.png")
            except:
                pass
            raise

async def custom_automation():
    """
    Add your custom automation logic here.
    This is where you'll implement your specific RPA tasks.
    """
    async with BrowserManager() as browser_manager:
        try:
            # Create browser context and page
            context = await browser_manager.create_context()
            page = await browser_manager.create_page(context)
            
            logger.info("Starting custom automation...")
            
            # TODO: Add your automation logic here
            # Examples:
            # - await page.goto("https://your-target-website.com")
            # - await page.fill("input[name='username']", "your_username")
            # - await page.click("button[type='submit']")
            # - await browser_manager.random_delay()
            # - await browser_manager.take_screenshot("step1.png")
            
            logger.info("Custom automation completed!")
            
        except Exception as e:
            logger.error(f"Custom automation failed: {e}")
            raise

async def main():
    """Main entry point"""
    setup_logging()
    logger.info("=== Green button session Started ===")
    logger.info(f"Configuration: Headless={settings.headless}, Browser={settings.browser_type}")
    
    try:
        # Run example automation
        await example_automation()
        
        # Uncomment to run your custom automation
        # await custom_automation()
        
    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
    except Exception as e:
        logger.error(f"Session failed: {e}")
        return 1
    finally:
        logger.info("=== RPA Session Finished ===")
    
    return 0

if __name__ == "__main__":
    # Run the async main function
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
