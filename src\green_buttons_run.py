"""
Green Buttons RPA Script
"""
import asyncio
import sys
from pathlib import Path
from loguru import logger
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from src.utils.browser_manager import Browser<PERSON>anager

def setup_logging():
    """Configure logging for green buttons script"""
    # Remove default handler
    logger.remove()
    
    # Add console handler
    logger.add(
        sys.stderr,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>GREEN_BUTTONS</cyan> - <level>{message}</level>"
    )
    
    # Add file handler with specific name for this script
    log_file = f"logs/green_buttons_{datetime.now().strftime('%Y%m%d')}.log"
    logger.add(
        log_file,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | GREEN_BUTTONS - {message}",
        rotation="10 MB",
        retention="7 days"
    )

async def green_buttons_automation(target_url=None):
    """
    Main automation function for green buttons processing.
    
    Args:
        target_url: URL to navigate to (optional)
    """
    async with BrowserManager() as browser_manager:
        try:
            # Create browser context and page
            context = await browser_manager.create_context()
            page = await browser_manager.create_page(context)
            
            logger.info("Starting Green Buttons RPA automation...")
            
            
            
            logger.info("Green Buttons automation completed successfully!")
            
        except Exception as e:
            logger.error(f"Green Buttons automation failed: {e}")
            await browser_manager.take_screenshot("error_green_buttons.png")
            raise
