"""
Configuration settings for the RPA project.
"""
import os
from pathlib import Path
from typing import Optional, Dict, Any
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class RPASettings(BaseSettings):
    """RPA Configuration Settings"""
    
    # Browser Configuration
    headless: bool = Field(default=False, env="HEADLESS")
    browser_type: str = Field(default="chromium", env="BROWSER_TYPE")
    viewport_width: int = Field(default=1920, env="VIEWPORT_WIDTH")
    viewport_height: int = Field(default=1080, env="VIEWPORT_HEIGHT")
    user_agent: str = Field(
        default="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        env="USER_AGENT"
    )
    
    # Delays and Timeouts (in milliseconds)
    default_timeout: int = Field(default=30000, env="DEFAULT_TIMEOUT")
    page_load_timeout: int = Field(default=60000, env="PAGE_LOAD_TIMEOUT")
    min_delay: int = Field(default=1000, env="MIN_DELAY")
    max_delay: int = Field(default=3000, env="MAX_DELAY")
    
    # Stealth Configuration
    stealth_mode: bool = Field(default=True, env="STEALTH_MODE")
    disable_webgl: bool = Field(default=True, env="DISABLE_WEBGL")
    disable_webrtc: bool = Field(default=True, env="DISABLE_WEBRTC")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="logs/rpa.log", env="LOG_FILE")
    
    # Output Configuration
    output_dir: str = Field(default="outputs", env="OUTPUT_DIR")
    screenshot_dir: str = Field(default="outputs/screenshots", env="SCREENSHOT_DIR")
    download_dir: str = Field(default="outputs/downloads", env="DOWNLOAD_DIR")
    
    # Proxy Configuration (optional)
    proxy_server: Optional[str] = Field(default=None, env="PROXY_SERVER")
    proxy_username: Optional[str] = Field(default=None, env="PROXY_USERNAME")
    proxy_password: Optional[str] = Field(default=None, env="PROXY_PASSWORD")
    
    # Custom Headers
    custom_headers: Optional[Dict[str, str]] = Field(default=None, env="CUSTOM_HEADERS")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    def get_proxy_config(self) -> Optional[Dict[str, Any]]:
        """Get proxy configuration if available"""
        if not self.proxy_server:
            return None
        
        config = {"server": self.proxy_server}
        if self.proxy_username and self.proxy_password:
            config.update({
                "username": self.proxy_username,
                "password": self.proxy_password
            })
        return config
    
    def ensure_directories(self):
        """Ensure all required directories exist"""
        directories = [
            self.output_dir,
            self.screenshot_dir,
            self.download_dir,
            os.path.dirname(self.log_file)
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

# Global settings instance
settings = RPASettings()
