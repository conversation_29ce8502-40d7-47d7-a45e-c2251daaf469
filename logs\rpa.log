2025-06-14 16:29:10 | INFO     | __main__:main:126 - === RPA Script Started ===
2025-06-14 16:29:10 | INFO     | __main__:main:127 - Configuration: Headless=False, Browser=chromium
2025-06-14 16:29:10 | INFO     | src.utils.browser_manager:start:33 - Starting browser manager...
2025-06-14 16:29:15 | INFO     | src.utils.browser_manager:start:70 - <PERSON><PERSON>er launched: chromium
2025-06-14 16:29:15 | INFO     | src.utils.browser_manager:create_context:105 - Browser context created with stealth settings
2025-06-14 16:29:16 | INFO     | src.utils.browser_manager:create_page:120 - Stealth plugins applied to page
2025-06-14 16:29:16 | INFO     | __main__:example_automation:47 - Starting example automation...
2025-06-14 16:29:18 | INFO     | src.utils.browser_manager:take_screenshot:187 - Screenshot saved: outputs/screenshots/example_page.png
2025-06-14 16:29:18 | INFO     | __main__:example_automation:58 - Page loaded successfully. Content length: 285
2025-06-14 16:29:18 | INFO     | __main__:example_automation:64 - Detected User Agent: {
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}

2025-06-14 16:29:21 | INFO     | src.utils.browser_manager:take_screenshot:187 - Screenshot saved: outputs/screenshots/headers_page.png
2025-06-14 16:29:21 | INFO     | __main__:example_automation:77 - Headers information extracted successfully
2025-06-14 16:29:21 | INFO     | __main__:example_automation:83 - Headers saved to: outputs/headers_info.txt
2025-06-14 16:29:21 | INFO     | __main__:example_automation:85 - Example automation completed successfully!
2025-06-14 16:29:22 | INFO     | src.utils.browser_manager:close:209 - Browser manager closed successfully
2025-06-14 16:29:22 | INFO     | __main__:main:142 - === RPA Script Finished ===
