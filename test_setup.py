"""
Quick test script to verify the RPA setup is working correctly.
"""
import asyncio
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from src.utils.browser_manager import BrowserManager

async def test_basic_functionality():
    """Test basic browser functionality"""
    print("🧪 Testing RPA Setup...")
    
    async with BrowserManager() as browser_manager:
        try:
            # Test browser creation
            context = await browser_manager.create_context()
            page = await browser_manager.create_page(context)
            
            print("✅ Browser launched successfully")
            
            # Test navigation
            await page.goto("https://httpbin.org/user-agent")
            print("✅ Page navigation working")
            
            # Test element interaction
            content = await page.content()
            if "user-agent" in content.lower():
                print("✅ Page content extraction working")
            
            # Test screenshot
            await browser_manager.take_screenshot("test_screenshot.png")
            print("✅ Screenshot functionality working")
            
            print("🎉 All tests passed! Your RPA setup is ready to use.")
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_basic_functionality())
    if success:
        print("\n🚀 You can now start building your RPA automation!")
        print("📝 Edit src/main.py to add your custom automation logic.")
    else:
        print("\n⚠️  Setup verification failed. Please check the error messages above.")
